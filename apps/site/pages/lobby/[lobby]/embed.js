import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import styles from '../../../styles/Embed.module.scss'
import React from 'react'
import { NextSeo } from 'next-seo'
import { useMainMatchData } from '../../../utils/dataFetching'
import { EmbedPageContent } from '../../../components/EmbedPageContent'
import 'iframe-resizer/js/iframeResizer.contentWindow'
import { ChartLoader } from '../../../components/Svg'
import dynamic from 'next/dynamic'
import { EmbedLayout } from '../../../components/EmbedLayout'
import { filterPublicHeaders } from '../../../utils/headers'

const BlockLoading = dynamic(() => import('../../../components/BlockLoading/BlockLoading'), {
  ssr: false,
})

const Embed = () => {
  const [isFetched, loadRecommendations] = useMainMatchData()

  return (
    <>
      <NextSeo noindex={true} />
      <BlockLoading
        isLoaded={isFetched}
        content={<EmbedPageContent loadRecommendations={loadRecommendations} />}
        loader={<ChartLoader className={styles.loader} />}
      />
    </>
  )
}

export async function getServerSideProps({ locale, req }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'embed', 'extension'])),
      headers: filterPublicHeaders(req.headers),
    },
  }
}

Embed.getLayout = function getLayout(page) {
  return <EmbedLayout>{page}</EmbedLayout>
}

export default Embed
