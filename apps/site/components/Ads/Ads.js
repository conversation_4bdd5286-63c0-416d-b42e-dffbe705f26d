/* eslint-disable react/jsx-no-target-blank */
import React, { useCallback } from 'react'
import styles from './Ads.module.scss'
import { useSelector } from 'react-redux'
import { trackPartnerClick } from '../../utils/analytics'
import { EMBED_QUERY_PARAMS } from 'common-consts'
import { useRouter } from 'next/router'
import { Refresh } from '../Svg'
import PropTypes from 'prop-types'

const Ads = ({ loadRecommendations }) => {
  const {
    query: { [EMBED_QUERY_PARAMS.STEAM_ID]: steamId },
  } = useRouter()

  const { recommendations } = useSelector(state => state.skins)
  const partnerLink =
    'https://cs.money/market/buy/?utm_source=mediabuy&utm_medium=flking&utm_campaign=market&utm_content=link'

  const handleLinkClick = useCallback(
    (skin, skinIndex) => () => {
      trackPartnerClick(steamId, skin, skinIndex)
    },
    [steamId],
  )

  return (
    <div className={styles.root}>
      <div className={styles.header}>
        <div>Inspired by your inventory</div>
        <a className={styles.partner} target="_blank" href={partnerLink} onClick={handleLinkClick()}>
          <span>Powered by</span>
          <img src="/assets/csmoney.webp" alt="partner logo" />
        </a>
      </div>
      <div className={styles.recommendationsGrid}>
        {recommendations.map((item, index) => (
          <a
            className={styles.recommendationCard}
            key={item.name}
            href={partnerLink + '&sort=price&order=asc&search=' + encodeURIComponent(item.name)}
            target="_blank"
            onClick={handleLinkClick(item, index)}
          >
            <div className={styles.recommendationCardContent}>
              <img src={item.image} alt={item.name} className={styles.skinImage} />
              <div className={styles.skinPrice}>
                Starting <span>${item.price.toFixed(2)}</span>
              </div>
            </div>
          </a>
        ))}
      </div>
      <button className={styles.refresh} onClick={loadRecommendations}>
        <Refresh /> Refresh
      </button>
    </div>
  )
}

Ads.propTypes = {
  loadRecommendations: PropTypes.func.isRequired,
}

export default Ads
