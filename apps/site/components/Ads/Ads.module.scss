.root {
  padding: 8px 4px 4px;
  background: var(--lk-gray5);
  border-radius: 12px 12px 0 12px;
  margin: 4px 15px 0;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  align-items: center;
  border-bottom: 1px solid var(--lk-gray4);
  margin-bottom: 8px;
  padding: 0 6px 5px;
  line-height: 12px;
  color: var(--lk-gray1);
}

.partner {
  color: var(--lk-gray2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  font-size: 10px;

  &:hover, &:focus {
    color: var(--lk-gray2);
  }

  img {
    width: 60px;
    padding-left: 1px;
  }
}

.recommendationsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
}

.recommendationCard {
  background: var(--lk-darker);
  border-radius: 8px;
  height: 88px;
  width: 86px;
  position: relative;
  transition: all 0.5s ease;
  overflow: hidden;
  background-image: linear-gradient(180deg, #262630 4.73%, #33333F 49.19%, #FCC01B 95.39%);

  &:hover {
    transform: translateY(-5px);
  }

  &::before {
    position: absolute;
    content: "";
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-image: linear-gradient(180deg, #262630 -51.98%, #33333F 18.73%, #FCC01B 92.21%);
    z-index: -1;
    transition: opacity 0.5s linear;
    opacity: 0;
  }

  &:hover::before {
    opacity: 1;
  }
}

.recommendationCardContent {
  margin: 1px;
  background-image: linear-gradient(180deg, #2C2C37 -34.48%, #262630 99.44%);
  width: 84px;
  height: 86px;
  border-radius: 8px;
  position: relative;
  overflow: hidden;

  &:hover {
    background-image: linear-gradient(179.36deg, #2A2A35 -34.48%, #1E1E26 99.44%);
  }
}

.skinPrice {
  font-weight: 500;
  font-size: 9px;
  color: var(--lk-gray1);
  position: absolute;
  bottom: 6px;
  left: 8px;
  line-height: 13px;
  white-space: pre-wrap;

  span {
    color: var(--lk-white);
    font-size: 12px;
    display: block;
  }
}

.skinImage {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.refresh {
  position: absolute;
  cursor: pointer;
  right: 0;
  bottom: 0;
  transform: translate(0, 100%);
  background: var(--lk-gray5);
  border: none;
  border-radius: 0 0 12px 12px;
  color: var(--lk-gray2);
  font-size: 12px;
  padding: 4px 8px 6px 8px;
  display: flex;
  align-items: center;

  svg {
    margin-right: 4px;
    fill: var(--lk-gray2);
  }
}