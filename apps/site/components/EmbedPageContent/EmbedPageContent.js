import React, { useEffect } from 'react'
import { MapStatistic } from '../MapStatistic'
import { PERIOD } from '../../consts/period'
import { useDispatch, useSelector } from 'react-redux'
import PropTypes from 'prop-types'
import { useMapList } from '../../utils/maps'
import { Compact<PERSON>hart } from '../MapStatistic/components/Chart/components/CompactChart'
import { excludeMaps, setSort } from '../../store/stats'
import { EmbedHeader } from '../EmbedHeader'
import { EmbedFooter } from '../EmbedFooter'
import { useRouter } from 'next/router'
import { TEAMS } from '../../consts/teams'
import { SORTING_DIR } from '../../consts/stats'
import { CHART_HEADER_KEYS, EMBED_QUERY_PARAMS } from 'common-consts'
import { selectActiveTeam } from '../../store/match'
import { Ads } from '../Ads'

const EmbedPageContent = ({ loadRecommendations }) => {
  const {
    query: { [EMBED_QUERY_PARAMS.USER_ID]: userId, [EMBED_QUERY_PARAMS.SORT_KEY]: sortKey },
  } = useRouter()

  const activePeriod = PERIOD.RECENT

  const dispatch = useDispatch()
  const match = useSelector(state => state.match.data)
  const showAds = useSelector(state => state.skins.showAds)
  const activeTeam = useSelector(selectActiveTeam(userId))
  const periodStats = useSelector(state => state.stats[activePeriod])

  const [mapPoolMaps, otherMaps, filteredPeriodStats] = useMapList(match, activePeriod, periodStats)

  useEffect(() => {
    dispatch(excludeMaps(otherMaps.map(map => map.map)))
  }, [dispatch, otherMaps])

  useEffect(() => {
    if (sortKey && activeTeam) {
      const diffDir = activeTeam === TEAMS.TEAM_A ? SORTING_DIR.DESC : SORTING_DIR.ASC

      dispatch(
        setSort({
          key: sortKey,
          team: sortKey !== CHART_HEADER_KEYS.DIFFERENCE ? activeTeam : undefined,
          dir: sortKey === CHART_HEADER_KEYS.DIFFERENCE ? diffDir : SORTING_DIR.DESC,
        }),
      )
    }
  }, [activeTeam, dispatch, sortKey])

  return (
    <>
      <EmbedHeader activeTeam={activeTeam} />
      <MapStatistic
        maps={[...mapPoolMaps, ...otherMaps]}
        periodStats={filteredPeriodStats}
        activePeriod={activePeriod}
        presenter={CompactChart}
      />
      {showAds && <Ads loadRecommendations={loadRecommendations} />}
      <EmbedFooter />
    </>
  )
}

EmbedPageContent.propTypes = {
  className: PropTypes.string,
  loadRecommendations: PropTypes.func.isRequired,
}

export default EmbedPageContent
