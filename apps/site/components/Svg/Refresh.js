import PropTypes from 'prop-types'

const GuideArrow = ({ className }) => (
  <svg className={className} viewBox="0 0 540 124" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.5 6.5C11.6667 48.5 104 121.5 271 121.5C438 121.5 524 45.5 537.5 1.5"
      stroke="#2D2D38"
      strokeWidth="4"
      strokeLinejoin="round"
      strokeDasharray="10 10"
    />
    <path d="M133.152 92L139.015 104.346L126 108.518" stroke="#2D2D38" strokeWidth="4" strokeLinecap="round" />
  </svg>
)

GuideArrow.propTypes = {
  className: PropTypes.string,
}

export default GuideArrow
