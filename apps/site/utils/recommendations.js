import { PartnerService } from './partnerService'
import { ERRORS } from '../consts/errors'
import { displaySubtypes, TYPES_FOR_DEDUPLICATION } from '../consts/skins'

const partnerService = new PartnerService()

// 10‑minute in‑memory cache for partner items
let partnerItemsCache = { data: null, expires: 0 }

async function getPartnerItems() {
  const now = Date.now()
  if (partnerItemsCache.data && partnerItemsCache.expires > now) {
    return partnerItemsCache.data
  }
  const items = await partnerService.getCachedItems()
  partnerItemsCache = { data: items, expires: now + 10 * 60 * 1000 }
  return items
}

/** Convenience helper to build JSON Responses */
const json = (data, status = 200) =>
  new Response(JSON.stringify(data), {
    status,
    headers: { 'content-type': 'application/json;charset=utf-8' },
  })

// ─────────────────────────────────────────────────────────────
//            Edge‑compatible handler (Request → Response)
// ─────────────────────────────────────────────────────────────
export async function GET(request) {
  const url = new URL(request.url)
  const steamId = url.searchParams.get('steamId')

  if (!steamId) {
    return json({ error: 'Steam ID is required' }, 400)
  }

  try {
    // Get user's CS inventory
    const [inventoryResponse, items] = await Promise.all([
      fetch(`https://steamcommunity.com/inventory/${steamId}/730/2?l=english`, { next: { revalidate: 64800 } }),
      getPartnerItems(),
    ])

    if (!inventoryResponse.ok) {
      if ([404, 401, 403].includes(inventoryResponse.status)) {
        return json({ error: ERRORS.INVENTORY_NOT_FOUND_OR_PRIVATE }, 404)
      }
      throw new Error(`Steam API returned ${inventoryResponse.status}`)
    }

    const inventory = await inventoryResponse.json()

    if (!inventory.descriptions) {
      return json({ error: ERRORS.INVENTORY_NOT_FOUND_OR_PRIVATE }, 404)
    }

    // Analyze inventory and generate recommendations
    return json(generateRecommendations(items, inventory.descriptions))
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error', error)
    return json({ error: 'Failed to fetch recommendations' }, 500)
  }
}

function generateRecommendations(items, userInventory) {
  // Calculate inventory statistics
  const inventoryStats = analyzeInventory(userInventory, items)

  // Get user's current items to exclude
  const userItems = new Set(inventoryStats.inventoryWithPrices.map(item => item.name))

  // Track seen base skin names to avoid duplicates
  const seenSkins = new Set()

  const isColorStatsEmpty = Object.keys(inventoryStats.colorStats).length === 0

  // Create price tiers with exponential growth
  const { min, max } = inventoryStats.priceRange

  // First, collect all valid items in a single array
  const allValidItems = Object.values(items)
    .map(item => {
      // Get base skin name and skip if we've seen it before
      if (seenSkins.has(item.name)) return null
      seenSkins.add(item.name)

      // Skip if user already has this item
      if (userItems.has(item.name)) return null

      // Skip if item is outside price range
      if (item.price > max || item.price < min) return null

      // Check if user has items of this subtype
      const hasSubtype = inventoryStats.inventoryWithPrices.some(invItem => invItem.subtype === item.subtype)
      if (!hasSubtype && !(displaySubtypes[item.subtype] ?? true)) return null

      // Calculate scores
      const colorScore =
        item.colors?.reduce((maxScore, color) => {
          const currentColorCount = inventoryStats.colorStats[color] || 0
          return Math.max(maxScore, currentColorCount)
        }, 0) || 0

      const familyScore = (inventoryStats.familyStats[item.family] || 0) * 10
      const score = Math.max(colorScore, familyScore)

      // Skip if no matching preferences
      if (score === 0 && !isColorStatsEmpty) return null

      return {
        ...item,
        matchingColors: item.colors?.filter(color => inventoryStats.colorStats[color]) || [],
        matchingFamily: inventoryStats.familyStats[item.family] ? item.family : null,
        score,
        scoreDetails: {
          colorScore,
          familyScore,
        },
      }
    })
    .filter(item => item !== null)
    .sort((a, b) => a.price - b.price)

  // Split into three equal chunks
  const chunkSize = Math.ceil(allValidItems.length / 3)
  const validItems = {
    low: allValidItems.slice(0, chunkSize),
    medium: allValidItems.slice(chunkSize, chunkSize * 2),
    high: allValidItems.slice(chunkSize * 2),
  }

  // Select recommendations from high to low tier, avoiding same types/subtypes
  const selectedRecommendations = []
  const usedTypes = new Set()
  const usedSubtypes = new Set()

  // Process tiers in order: high -> medium -> low
  for (const tier of ['high', 'medium', 'low']) {
    const availableItems = validItems[tier].filter(item => {
      // For knives and gloves, check by type
      if (TYPES_FOR_DEDUPLICATION.includes(item.type)) {
        return !usedTypes.has(item.type)
      }
      // For other items, check by subtype
      return !usedSubtypes.has(item.subtype)
    })

    validItems[tier] = availableItems

    if (availableItems.length > 0) {
      const selected = weightedRandomSelect(availableItems)
      selectedRecommendations.push(selected)

      // Track used type/subtype based on item category
      if (TYPES_FOR_DEDUPLICATION.includes(selected.type)) {
        usedTypes.add(selected.type)
      } else {
        usedSubtypes.add(selected.subtype)
      }
    }
  }

  return prepareResponse(selectedRecommendations.reverse(), validItems, inventoryStats)
}

function analyzeInventory(userInventory, partnerItems) {
  // Build a Map keyed by name → most‑expensive item in O(n)
  const byName = new Map()
  for (const userItem of userInventory) {
    const partnerItem = partnerItems[userItem.market_hash_name]
    if (!partnerItem) continue
    const existing = byName.get(partnerItem.name)
    if (!existing || partnerItem.price > existing.price) {
      byName.set(partnerItem.name, partnerItem)
    }
  }
  const inventoryWithPrices = [...byName.values()]
    .sort((a, b) => b.price - a.price) // Desc
    .slice(0, 20) // Top 20

  const totalValue = inventoryWithPrices.reduce((sum, item) => sum + item.price, 0)
  const itemCount = inventoryWithPrices.length
  const averageItemValue = itemCount > 0 ? totalValue / itemCount : 0
  const maxItemValue = inventoryWithPrices[0]?.price || 0 // First item after sort is most expensive
  const minItemValue = inventoryWithPrices[itemCount - 1]?.price || 0 // Last item of top 20

  const colorStats = computeColorStats(inventoryWithPrices)
  const familyStats = computeFamilyStats(inventoryWithPrices)

  const priceRange = calculatePriceRange(averageItemValue, maxItemValue)

  return {
    totalValue,
    itemCount,
    averageItemValue,
    maxItemValue,
    minItemValue,
    priceRange,
    inventoryWithPrices,
    colorStats,
    familyStats,
  }
}

function weightedRandomSelect(items) {
  if (!items.length) return null

  // Calculate total weight (sum of all scores)
  const totalWeight = items.reduce((sum, item) => sum + item.score, 0)

  // Generate random value between 0 and total weight
  let random = Math.random() * totalWeight

  // Find the item that corresponds to the random value
  for (const item of items) {
    random -= item.score
    if (random <= 0) {
      return item
    }
  }

  // Fallback to last item (shouldn't happen unless rounding errors)
  return items[items.length - 1]
}

function computeColorStats(inventory) {
  // Calculate color statistics
  const colorStats = inventory.reduce((stats, item) => {
    if (item.colors) {
      item.colors.forEach(color => {
        stats[color] = (stats[color] || 0) + 1
      })
    }
    return stats
  }, {})

  // Convert to array and sort by count descending
  const sortedColors = Object.entries(colorStats).sort(([, countA], [, countB]) => countB - countA)

  // If we have less than 2 colors, return all
  if (sortedColors.length <= 2) {
    return Object.fromEntries(sortedColors)
  }

  // Get the count of the second highest color
  const secondHighestCount = sortedColors[1][1]

  // Filter to keep only top colors and those tied with second place
  return sortedColors
    .filter(([, count], index) => index === 0 || count >= secondHighestCount)
    .reduce((acc, [color, count]) => {
      acc[color] = count
      return acc
    }, {})
}

function computeFamilyStats(inventory) {
  return inventory.reduce((stats, item) => {
    if (item.family) {
      stats[item.family] = (stats[item.family] || 0) + 1
    }
    return stats
  }, {})
}

function calculatePriceRange(averageItemValue, maxItemValue) {
  return {
    min: Math.min(averageItemValue * 0.1, 20),
    max: Math.max(maxItemValue * 1.25, 5), // 125% of max value
  }
}

function prepareResponse(recommendations, validItems, inventoryStats) {
  if (process.env.NODE_ENV !== 'production') {
    return {
      recommendations,
      debug: {
        ...inventoryStats,
        validItems,
      },
    }
  }

  const filteredRecommendations = recommendations.map(item => ({
    name: item.name,
    image: item.image,
    price: item.price,
  }))

  return {
    recommendations: filteredRecommendations,
  }
}
