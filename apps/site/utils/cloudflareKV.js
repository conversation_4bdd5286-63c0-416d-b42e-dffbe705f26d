const DEFAULT_CACHE_KEY = 'cs_skins_cache'
const CACHE_KEY = process.env.CLOUDFLARE_SKINS_CACHE_KEY || DEFAULT_CACHE_KEY

export class CloudflareKV {
  constructor() {
    this.accountId = process.env.CLOUDFLARE_ACCOUNT_ID
    this.namespaceId = process.env.CLOUDFLARE_NAMESPACE_ID
    this.apiToken = process.env.CLOUDFLARE_API_TOKEN
  }

  async set(data) {
    try {
      const response = await fetch(
        `https://api.cloudflare.com/client/v4/accounts/${this.accountId}/storage/kv/namespaces/${this.namespaceId}/values/${CACHE_KEY}`,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            data,
            timestamp: Date.now(),
          }),
        },
      )

      if (!response.ok) {
        throw new Error(`Failed to set KV: ${response.statusText}`)
      }

      return true
    } catch (error) {
      console.error('CloudflareKV set error:', error)
      return false
    }
  }

  async get() {
    try {
      const response = await fetch(
        `https://api.cloudflare.com/client/v4/accounts/${this.accountId}/storage/kv/namespaces/${this.namespaceId}/values/${CACHE_KEY}`,
        {
          next: { revalidate: 600 },
          headers: {
            Authorization: `Bearer ${this.apiToken}`,
          },
        },
      )

      if (!response.ok) {
        throw new Error(`Failed to get KV: ${response.statusText}`)
      }

      return response.json()
    } catch (error) {
      console.error('CloudflareKV get error:', error)
      return null
    }
  }
}
